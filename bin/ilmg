#!/usr/bin/env bash

# shellcheck disable=SC1090
source <(curl -sSL https://is.gd/anumof)

# all options
# "base" "min" "shell-slim" "shell-slim-ui" "shell" "shell-ui" "dev" "vbox-dev" "vm" "vm-ui" "prog" "all" "nix" "sway" "hyprland" "slimbox" "box" "fullbox" "dbox" "nixbox" "tw-wslbox" "nixos-wslbox" "wslbox" "wsl" "centos-wsl" "centos-fast" "generic-ct" "generic" "fedora-atomic-tbox" "ublue" "fedora-atomic" "fedora-layered" "dbox-docker-dev" "box-default" "box-default-atomic"

INSTALL_OPTIONS=("dev" "nix" "shell-slim" "shell" "shell-ui" "vm" "vm-ui")

help() {
    echo "Usage: $0 [OPTION]"
    echo "Options:"
    echo "  dev             shell, vscode, docker and terminal."
    echo "  nix             nix with home-manager"
    echo "  shell-slim      essential shell packages."
    echo "  shell-slim-ui   essential shell packages with modern terminal."
    echo "  shell           min and shell tools and config for zsh, tmux, neovim."
    echo "  shell-ui        shell, fonts and modern terminal."
    echo "  vm              vm tools(libvirt for linux and orbstack for macos)."
    echo "  vm-ui           vm and virt-manager and gnome-boxes."
    echo "  sway            sway desktop on fedora and tumbleweed."
    echo "  hyprland        hyprland desktop on fedora and tumbleweed."
    echo "  help            shows this help."
    echo ""
}

main() {
    if ! [[ "$#" -eq 0 ]]; then
        "${1}_groupstall"
        return 0
    fi

    if ! has_cmd gum; then
        help
        exit 0
    fi

    local SELECTED_OPTION
    SELECTED_OPTION=$(gum choose --header "Choose option to install" "${INSTALL_OPTIONS[@]}")

    if [[ -n "$SELECTED_OPTION" ]]; then
        echo ""
        slog "Selected: $SELECTED_OPTION. Installing..."
        echo ""
        "${SELECTED_OPTION}_groupstall"
        slog "Installation complete."
    else
        slog "No option selected."
        return 1
    fi
}

if [[ "$1" == "help" ]]; then
    help
else
    CLICOLOR_FORCE=1 bootstrap "$@"
fi
