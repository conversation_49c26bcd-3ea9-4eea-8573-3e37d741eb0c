#!/usr/bin/env bash

if command -v curl >/dev/null; then
    # shellcheck disable=SC1090
    source <(curl -sSL https://is.gd/anumof)
elif command -v wget >/dev/null; then
    # shellcheck disable=SC1090
    source <(wget -qO- https://is.gd/anumof)
else
    echo "curl or wget not found. Please install curl or wget and try again."
    exit 1
fi

# Check if the script is run as root
[[ "$(id -u)" != "0" ]] && err_exit "This script must be run as root" 1>&2

# Determine the package manager
if is_apt; then
    pkg_manager="apt-get"
    ssh_service="ssh"
elif is_rh; then
    pkg_manager="dnf"
    ssh_service="sshd"
else
    err_exit "Unsupported system"
fi

# Check if SSH server is installed
if ! command -v sshd &>/dev/null; then
    slog "SSH server is not installed. Installing now..."
    $pkg_manager install -y openssh-server
fi

# Start the SSH server
systemctl enable --now "$ssh_service"

slog "SSH server is now running."
