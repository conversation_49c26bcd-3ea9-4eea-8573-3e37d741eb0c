#!/usr/bin/env bash

# shellcheck disable=SC1090
source <(curl -sSL https://is.gd/anumof)

# all options
# "bash" "zsh-min" "zsh" "tmux" "emacs-doom" "emacs" "emacs-slim" "nvim" "git" "vscode" "alacritty" "wezterm" "kitty" "ghostty" "atuin" "yazi" "shell" "mfoot" "gnome" "incus" "libvirt" "docker" "vscode-flatpak" "sway-waybar" "wlogout" "sway" "rofi" "sway-full" "hypr-waybar" "hyprland" "amethyst" "aerospac"

INSTALL_OPTIONS=("nvim" "bash" "vscode_flatpak" "vscode" "zsh" "tmux" "emacs" "git" "gnome" "sway" "hyprland" "docker" "libvirt")

SELECTED_OPTIONS=("zsh" "tmux" "nvim")

help() {
    echo "Usage: $0 [OPTION1] [OPTION2] ..."
    echo "Options:"
    echo "  zsh         zsh config with starship prompt and common plugins."
    echo "  tmux        tmux config, catppuccin theme and common plugins."
    echo "  nvim        lazyvim based config and plugins."
    echo "  vscode      vscode settings and extensions."
    echo "  emacs       doom emacs config."
    echo "  git         git basic config."
    echo "  sway        sway config with modern desktop tools."
    echo "  hyprland    hyprland config with modern desktop tools."
    echo "  gnome       gnome config and extensions."
    echo "  docker      docker config to use without sudo."
    echo "  libvirt     libvirt config to use without sudo."
    echo "  help        show this help."
    echo ""
    echo ""
}

ilmc() {
    while [[ "$#" -gt 0 ]]; do
        if fn_exists "${1}_confstall"; then
            "${1}_confstall"
        else
            err_exit "No such config: $1"
        fi
        shift
    done
}

main() {
    if ! [[ "$#" -eq 0 ]]; then
        ilmc "$@"
        return 0
    fi

    if ! has_cmd gum; then
        help
        exit 0
    fi

    readarray -t SELECTED_OPTIONS < <(
        gum choose \
            --no-limit \
            --selected="$SELECTED_CSV" \
            --header "Choose options to install" \
            "${INSTALL_OPTIONS[@]}"
    )

    if [[ ${#SELECTED_OPTIONS[@]} -gt 0 ]]; then
        echo ""
        slog "Selected: ${SELECTED_OPTIONS[*]}. Configuring..."
        echo ""
        ilmc "${SELECTED_OPTIONS[@]}"
        slog "Configuration complete."
    else
        slog "No options selected."
        return 1
    fi
}

CLICOLOR_FORCE=1 bootstrap "$@"
