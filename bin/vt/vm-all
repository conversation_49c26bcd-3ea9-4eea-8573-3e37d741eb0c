#!/usr/bin/env bash

# shellcheck disable=SC1091

set -euo pipefail

source "$(dirname "$0")/vm-utils"
source "$(dirname "$0")/all-utils"

VM_DISTRO_LIST=("ubuntu" "fedora" "debian" "arch") # "alpine" not working

all_create() {
    slog "Creating 4 libvirt VMs (Ubuntu, Fedora, Alpine, Debian)..."

    for distro in "${VM_DISTRO_LIST[@]}"; do
        if vm_exists "${distro}-vm"; then
            slog "$distro VM already exists, skipping..."
        else
            slog "Creating $distro VM: $distro-vm"
            vm-create --distro "$distro" --name "${distro}-vm"
        fi
    done

    slog "Listing created VMs:"
    vm_list

    success "All VMs created successfully!"
    slog "You can access them using: virsh console <vm-name>"
}

usage() {
    all_usage "libvirt" "VMs"
}

all_delete() {
    slog "Deleting all VMs..."
    for vm in "${VM_DISTRO_LIST[@]}"; do
        if vm_exists "${vm}-vm"; then
            slog "Deleting VM: ${vm}-vm"
            vm delete "${vm}-vm"
        fi
    done
    success "All VMs deleted successfully!"
}

all_start() {
    slog "Starting all VMs..."
    for vm in "${VM_DISTRO_LIST[@]}"; do
        if vm_exists "${vm}-vm"; then
            slog "Starting VM: ${vm}-vm"
            vm start "${vm}-vm"
        fi
    done
    success "All VMs started successfully!"
}

all_stop() {
    slog "Stopping all VMs..."
    for vm in "${VM_DISTRO_LIST[@]}"; do
        if vm_exists "${vm}-vm"; then
            slog "Stopping VM: ${vm}-vm"
            vm stop "${vm}-vm"
        fi
    done
    success "All VMs stopped successfully!"
}

all_restart() {
    slog "Restarting all VMs..."
    for vm in "${VM_DISTRO_LIST[@]}"; do
        if vm_exists "${vm}-vm"; then
            slog "Restarting VM: ${vm}-vm"
            vm restart "${vm}-vm"
        fi
    done
    success "All VMs restarted successfully!"
}

main() {
    virt_check_prerequisites
    all_parse_args "$@"
}

main "$@"
