#!/usr/bin/env bash

# shellcheck disable=SC1091
source "$(dirname "$0")/vt-utils"

virt_check_prerequisites() {
  local missing_tools=()
  for tool in virsh virt-install qemu-img wget xorriso; do
    if ! has_cmd "$tool"; then
      missing_tools+=("$tool")
    fi
  done

  if [[ ${#missing_tools[@]} -gt 0 ]]; then
    fail "Missing required tools: ${missing_tools[*]}"
    slog "Install with: vm install"
    exit 1
  fi

  if ! systemctl is-active --quiet libvirtd; then
    fail "libvirtd service is not running"
    slog "Starting with: sudo systemctl start libvirtd"
    sudo systemctl start libvirtd
    sleep 1
    if ! systemctl is-active --quiet libvirtd; then
      fail "Failed to start libvirtd service"
      exit 1
    fi

  fi

  if ! groups | grep -q libvirt; then
    fail "User not in libvirt group. You may need sudo for virsh commands"
    fail "Add user to group: sudo usermod -a -G libvirt \$USER"
    exit 1
  fi

}

vm_check_prerequisites() {
  virt_check_prerequisites

  if ! has_cmd vm-create; then
    fail "vm-create is not in PATH."
    exit 1
  fi

  if ! has_cmd vm; then
    fail "vm is not in PATH."
    exit 1
  fi
}

vm_list() {
  slog "Listing all VMs..."
  echo
  if has_cmd virsh; then
    virsh list --all
  else
    fail "virsh command not found. Please install virtualization tools first."
    slog "You can install them with: vm install"
    return 1
  fi
}

vm_exists() {
  virsh dominfo "$1" &>/dev/null
}

vm_check_exists() {
  local vm_name="$1"

  vm_exists "$vm_name" && return 0

  fail "VM '$vm_name' not found"
  return 1
}

vm_state() {
  virsh domstate "$vm_name"
}

vm_ip() {
  local vm_name="$1"

  vm_exists "$vm_name" || return 1

  local state
  state=$(virsh domstate "$vm_name")

  if [[ "$state" != "running" ]]; then
    return 2
  fi

  local ip
  ip=$(virsh domifaddr "$vm_name" --source agent | awk '/enp|eth/ {sub(/\/.*/, "", $4); print $4}')

  if [[ -z "$ip" ]]; then
    ip=$(virsh net-dhcp-leases default 2>/dev/null | grep "$vm_name" | awk '{print $5}' | cut -d'/' -f1 | head -1)
  fi

  if [[ -z "$ip" ]]; then
    return 3
  fi

  echo "$ip"
  return 0
}

ip_errors() {
  local ret=$1
  local vm_name=$2

  if [[ $ret -eq 1 ]]; then
    fail "VM '$vm_name' not found"
    return 1
  elif [[ $ret -eq 2 ]]; then
    fail "VM '$vm_name' is not running"
    return 1
  elif [[ $ret -eq 3 ]]; then
    fail "Could not determine IP address for VM '$vm_name'"
    return 1
  fi

}

vm_ssh() {
  local vm_name="$1"
  local username="${2:-}"
  vm_check_exists "$vm_name" || return 1

  if [[ -z "$vm_name" ]]; then
    fail "VM name not provided. Usage: vm ssh <vm_name> [username]"
    return 1
  fi

  if [[ -z "$username" ]]; then
    fail "Username not provided. Usage: vm ssh <vm_name> [username]"
    return 1
  fi

  local state
  state=$(virsh domstate "$vm_name")

  if [[ "$state" != "running" ]]; then
    fail "VM '$vm_name' is not running"
    slog "Start it with: $0 start $vm_name"
    return 1
  fi

  local ip
  ip=$(vm_ip "$vm_name")
  local ret=$?
  ip_errors "$ret" "$vm_name"

  slog "Connecting to $vm_name ($ip) as $username..."
  ssh "$username@$ip"
}
