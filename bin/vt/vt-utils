#!/usr/bin/env bash

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

SUDO_KEEPALIVE_PID=""

start_sudo_keepalive() {
  slog "Starting sudo keepalive process..."
  sudo -v
  (while true; do
    sudo -v
    sleep 50
  done) &
  SUDO_KEEPALIVE_PID=$!
}

stop_sudo_keepalive() {
  if [ -n "$SUDO_KEEPALIVE_PID" ] && kill -0 "$SUDO_KEEPALIVE_PID" 2>/dev/null; then
    slog "Stopping sudo keepalive process..."
    kill "$SUDO_KEEPALIVE_PID" 2>/dev/null
  fi
  SUDO_KEEPALIVE_PID=""
}

check_tmux() {
  if ! has_cmd tmux; then
    fail "tmux is not installed. Please install it first."
    exit 1
  fi
}

incus_instance_exists() {
  incus list type=container,virtual-machine --format csv --columns n | grep -q "^$1$"
}

default_username() {
  local distro="$1"

  local username

  case "$distro" in
  ubuntu*) username="ubuntu" ;;
  fedora*) username="fedora" ;;
  centos*) username="centos" ;;
  debian*) username="debian" ;;
  arch*) username="arch" ;;
  alpine*) username="alpine" ;;
  nix*) username="nixos" ;;
  rocky*) username="rocky" ;;
  tumbleweed* | tw*) username="opensuse" ;;
  *)
    username="$USER"
    ;;
  esac

  echo "$username"
}

incus_check() {
  if ! has_cmd incus; then
    fail "incus command not found. Please install Incus first."
    exit 1
  fi

  if ! incus info >/dev/null 2>&1; then
    fail "Cannot connect to Incus daemon. Please ensure Incus is running and you have proper permissions."
    exit 1
  fi
}
