#!/usr/bin/env bash

# shellcheck disable=SC1091

set -euo pipefail

source "$(dirname "$0")/vm-utils"
source "$(dirname "$0")/all-utils"

VM_EASY_DISTRO_LIST=("ubuntu" "fedora" "arch" "debian")

all_create() {
  slog "Creating 4 libvirt VMs (Ubuntu, Fedora, Arch, Debian)..."

  for distro in "${VM_EASY_DISTRO_LIST[@]}"; do
    if vm_exists "${distro}-easy"; then
      slog "$distro VM already exists, skipping..."
    else
      slog "Creating $distro VM: $distro-easy"
      vm-easy --distro "$distro" --name "${distro}-easy"
    fi
  done

  success "All VMs created successfully!"
  slog "You can access them using: virsh console <vm-name>"
}

all_delete() {
  slog "Deleting all VMs..."
  for vm in "${VM_EASY_DISTRO_LIST[@]}"; do
    if vm_exists "${vm}-easy"; then
      slog "Deleting VM: ${vm}-easy"
      vm delete "${vm}-easy"
    fi
  done
  success "All VMs deleted successfully!"
}

all_start() {
  slog "Starting all VMs..."
  for vm in "${VM_EASY_DISTRO_LIST[@]}"; do
    if vm_exists "${vm}-easy"; then
      slog "Starting VM: ${vm}-easy"
      vm start "${vm}-easy"
    fi
  done
  success "All VMs started successfully!"
}

all_stop() {
  slog "Stopping all VMs..."
  for vm in "${VM_EASY_DISTRO_LIST[@]}"; do
    if vm_exists "${vm}-easy"; then
      slog "Stopping VM: ${vm}-easy"
      vm stop "${vm}-easy"
    fi
  done
  success "All VMs stopped successfully!"
}

all_restart() {
  slog "Restarting all VMs..."
  for vm in "${VM_EASY_DISTRO_LIST[@]}"; do
    if vm_exists "${vm}-easy"; then
      slog "Restarting VM: ${vm}-easy"
      vm restart "${vm}-easy"
    fi
  done
  success "All VMs restarted successfully!"
}

usage() {
  all_usage "libvirt" "VMs"
}

main() {
  virt_check_prerequisites
  all_parse_args "$@"
}

main "$@"
