#!/usr/bin/env bash

# shellcheck disable=SC1091

set -euo pipefail

source "$(dirname "$0")/ivm-utils"

# Default values
unset VM_NAME
unset DISTRO
unset RELEASE
unset USERNAME
unset <PERSON>SSWORD
unset SSH_KEY
unset IMAGE

VCPUS="3"
MEMORY_MB="4096"
DISK_SIZE="20GB"
BRIDGE_IF="incusbr0"
DOTFILES_OPTIONS="min"
INSTALL_NIX=false

usage() {
    cat <<EOF
Usage: $0 --distro DISTRO [OPTIONS]

Create Incus virtual machines with cloud-init and SSH access.

REQUIRED:
    --distro DISTRO         Distribution (ubuntu, fedora, arch, debian, centos, tumbleweed, alpine)

OPTIONS:
    --name NAME             VM name (default: distro name)
    --release RELEASE       Distribution release (default: latest)
    --username USER         Username for VM (default: distro default)
    --password PASS         User password (default: vm name)
    --vcpus NUM             Number of vCPUs (default: 4)
    --memory MB             RAM in MB (default: 4096)
    --disk-size SIZE        Disk size (default: 20GB)
    --ssh-key PATH          SSH public key path (default: auto-detect)
    --bridge BRIDGE         Network bridge (default: incusbr0)
    --nix                   Install Nix using Determinate Systems installer
    --dotfiles OPTIONS...   Install dotfiles with specified options (must be last; all following arguments are passed to dotfiles)
    --help, -h              Show this help

EXAMPLES:
    $0 --distro ubuntu
    $0 --distro fedora --name my-fedora --vcpus 4 --memory 4096
    $0 --distro debian --username admin --password mypass
    $0 --distro arch --release current --disk-size 40GB
    $0 --distro tumbleweed --name opensuse-vm --vcpus 2 --memory 4096
    $0 --distro ubuntu --nix            # Ubuntu VM with Nix package manager
    $0 --distro ubuntu --name ubuntu-dev --vcpus 4 --memory 8192 --dotfiles shell-slim docker code-server

SUPPORTED DISTRIBUTIONS:
    ubuntu      - Ubuntu LTS (24.04) or specified release
    fedora      - Fedora (latest) or specified release
    arch        - Arch Linux (current)
    debian      - Debian (12/bookworm) or specified release
    centos      - CentOS Stream (9) or specified release
    tumbleweed  - openSUSE Tumbleweed (rolling release)
    alpine      - Alpine Linux (latest) or specified release

EOF
}

configure_distribution() {
    case "$DISTRO" in
    ubuntu)
        RELEASE=${RELEASE:-"24.04"}
        IMAGE="images:ubuntu/${RELEASE}/cloud"
        ;;
    fedora)
        RELEASE=${RELEASE:-"42"}
        IMAGE="images:fedora/${RELEASE}/cloud"
        ;;
    arch)
        RELEASE=${RELEASE:-"current"}
        IMAGE="images:archlinux/${RELEASE}/cloud"
        ;;
    debian)
        RELEASE=${RELEASE:-"12"}
        IMAGE="images:debian/${RELEASE}/cloud"
        ;;
    centos)
        RELEASE=${RELEASE:-"9-Stream"}
        IMAGE="images:centos/${RELEASE}/cloud"
        ;;
    tumbleweed | tw)
        RELEASE=${RELEASE:-"current"}
        IMAGE="images:opensuse/tumbleweed/cloud"
        ;;
    nix)
        RELEASE=${RELEASE:-"unstable"}
        IMAGE="images:nixos/${RELEASE}/cloud"
        ;;
    rocky)
        RELEASE=${RELEASE:-"9"}
        IMAGE="images:rockylinux/${RELEASE}/cloud"
        ;;
    alpine)
        RELEASE=${RELEASE:-"3.22"}
        IMAGE="images:alpine/${RELEASE}/cloud"
        ;;
    *)
        fail "Unsupported distribution: $DISTRO"
        fail "Supported distributions: ubuntu, fedora, arch, debian, centos, tumbleweed, alpine"
        exit 1
        ;;
    esac

    VM_NAME=${VM_NAME:-"${DISTRO}-vm"}
    USERNAME=${USERNAME:=$(default_username "$DISTRO")}
    PASSWORD=${PASSWORD:-"$USERNAME"}
    PASSWORD_HASH=$(openssl passwd -6 "$PASSWORD")

    slog "Configuration:"
    slog "  Distribution: $DISTRO $RELEASE"
    slog "  VM Name: $VM_NAME"
    slog "  Username: $USERNAME"
    slog "  Image: $IMAGE"
    slog "  Resources: ${VCPUS} vCPUs, ${MEMORY_MB}MB RAM, ${DISK_SIZE} disk"
    slog "  Network Bridge: $BRIDGE_IF"
    if [[ -n "$DOTFILES_OPTIONS" ]]; then
        slog "  Dotfiles: $DOTFILES_OPTIONS"
    fi
}

generate_cloud_init_config() {
    slog "Generating cloud-init configuration..."

    # Create temporary directory for cloud-init files
    CLOUD_INIT_DIR=$(mktemp -d)
    trap '[[ -n "$CLOUD_INIT_DIR" && -d "$CLOUD_INIT_DIR" ]] && rm -rf "$CLOUD_INIT_DIR"' EXIT

    local pub_key
    pub_key=$(cat "$SSH_KEY")

    local packages_common="git trash-cli micro tree curl wget unzip htop zsh tmux"

    local openssh_pkg

    if [[ "$DISTRO" == "arch" ]]; then
        openssh_pkg="openssh"
    else
        openssh_pkg="openssh-server"
    fi

    # Prepare MOTD content
    local motd_content="Welcome to $VM_NAME ($DISTRO $RELEASE)!
      Created with ivm-create

      SSH access configured for user: $USERNAME"

    local runcmd_lines

    if [[ "$DISTRO" == "alpine" ]]; then
        runcmd_lines=(
            "rc-update add sshd default"
            "service sshd start"
        )
    else
        runcmd_lines=(
            "systemctl enable --now ssh || systemctl enable --now sshd || true"
        )
    fi

    # Add Nix installation if specified
    if [[ "$INSTALL_NIX" == "true" ]]; then
        slog "Adding Nix installation to cloud-init"
        motd_content+="

      Nix package manager is installed and ready to use.
      Quick commands:
        nix --version
        nix search <package>
        nix-env -iA nixpkgs.<package>
        nix-shell -p <package>"

        runcmd_lines+=(
            "curl --proto '=https' --tlsv1.2 -sSf -L https://install.determinate.systems/nix | sh -s -- install --no-confirm"
            "echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /etc/bash.bashrc"
            "echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /etc/profile"
            "echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /home/<USER>/.bashrc"
            "systemctl enable --now nix-daemon || true"
        )
    fi

    # Add dotfiles installation if specified
    if [[ -n "$DOTFILES_OPTIONS" ]]; then
        slog "Adding dotfiles installation to cloud-init with options: $DOTFILES_OPTIONS"
        motd_content+="

      Dotfiles are installed and ready to use.
      Location: /home/<USER>/.ilm
      Quick commands:
        ilmi shell
        ilmi docker
        ilmi python
        ilmc zsh
        ilmc tmux
        ilmc nvim"

        runcmd_lines+=(
            "su - $USERNAME -c 'bash -c \"\$(curl -sSL https://is.gd/egitif || wget -qO- https://is.gd/egitif)\" -- $DOTFILES_OPTIONS'"
        )
    fi

    # Create cloud-init user-data file using tee
    tee "${CLOUD_INIT_DIR}/user-data" >/dev/null <<EOF
#cloud-config
hostname: $VM_NAME
manage_etc_hosts: true

# User configuration
users:
  - name: $USERNAME
    groups:
      - sudo
      - wheel
      - adm
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: "$PASSWORD_HASH"
    ssh_authorized_keys:
      - "$pub_key"

# System configuration
package_update: true
package_upgrade: true

packages:
  - $openssh_pkg
$(for pkg in $packages_common; do echo "  - $pkg"; done)

runcmd:
$(printf "  - %s\n" "${runcmd_lines[@]}")

# Ensure SSH service is enabled
ssh_pwauth: true
disable_root: false

write_files:
  - path: /etc/motd
    content: |
      $motd_content

final_message: "VM $VM_NAME setup complete! SSH access is ready."
EOF

    # Create meta-data file
    tee "${CLOUD_INIT_DIR}/meta-data" >/dev/null <<EOF
instance-id: ${VM_NAME}-$(date +%s)
local-hostname: $VM_NAME
EOF

    success "Cloud-init configuration files created in: $CLOUD_INIT_DIR"
}

create_vm() {
    slog "Creating Incus VM '$VM_NAME'..."

    # Check if VM already exists
    if incus_instance_exists "$VM_NAME"; then
        fail "instance '$VM_NAME' already exists"
        exit 1
    fi

    # Generate cloud-init configuration files
    generate_cloud_init_config

    # Create the VM with cloud-init configuration
    slog "Launching VM with image: $IMAGE"

    # Build launch command with distribution-specific settings
    local launch_cmd=(
        incus launch "$IMAGE" "$VM_NAME" --vm
        --config "limits.cpu=$VCPUS"
        --config "limits.memory=${MEMORY_MB}MB"
        --config "user.user-data=$(cat "${CLOUD_INIT_DIR}/user-data")"
        --config "user.meta-data=$(cat "${CLOUD_INIT_DIR}/meta-data")"
        --device "root,size=$DISK_SIZE"
        --config "security.secureboot=false"
        --network "$BRIDGE_IF"
    )

    if ! "${launch_cmd[@]}"; then
        fail "Failed to create VM '$VM_NAME'"
        exit 1
    fi

    success "VM '$VM_NAME' created successfully"

    # Clean up temporary cloud-init directory
    if [[ -n "$CLOUD_INIT_DIR" && -d "$CLOUD_INIT_DIR" ]]; then
        rm -rf "$CLOUD_INIT_DIR"
        slog "Cleaned up temporary cloud-init files"
    fi
}

show_completion_info() {
    success "VM '$VM_NAME' is ready!"
    echo
    slog "VM Details:"
    incus list "$VM_NAME"
    echo

    local ip
    if ip=$(ivm_ip "$VM_NAME"); then
        slog "SSH Access:"
        slog "  IP Address: $ip"
        slog "  Username: $USERNAME"
        slog "  SSH Command: ssh $USERNAME@$ip"
        echo
        slog "You can also use Incus commands:"
        slog "  Console: incus console $VM_NAME"
        slog "  Execute: incus exec $VM_NAME -- <command>"
        slog "  Shell: incus exec $VM_NAME -- /bin/bash"
    else
        slog "Use Incus commands to access the VM:"
        slog "  Console: incus console $VM_NAME"
        slog "  Execute: incus exec $VM_NAME -- <command>"
        slog "  Shell: incus exec $VM_NAME -- /bin/bash"
    fi

    # Show Nix information if installed
    if [[ "$INSTALL_NIX" == "true" ]]; then
        echo
        slog "Nix Package Manager Information:"
        slog "  Nix is pre-installed on this VM"
        slog "  Check version:      nix --version"
        slog "  Search packages:    nix search <package>"
        slog "  Install package:    nix-env -iA nixpkgs.<package>"
        slog "  Temporary shell:    nix-shell -p <package>"
        slog "  Documentation: https://nixos.org/manual/nix/stable/"
    fi

    # Show dotfiles information if installed
    if [[ -n "$DOTFILES_OPTIONS" ]]; then
        echo
        slog "Dotfiles Information:"
        slog "  Dotfiles are pre-installed on this VM with options: $DOTFILES_OPTIONS"
        slog "  Location: ~/.ilm"
        slog "  Install shell:      ilmi shell"
        slog "  Install Docker:     ilmi docker"
        slog "  Install Python:     ilmi python"
        slog "  Config zsh:         ilmc zsh"
        slog "  Config tmux:        ilmc tmux"
        slog "  Config nvim:        ilmc nvim"
        slog "  Repository: https://github.com/pervezfunctor/dotfiles"
    fi

    echo
    slog "VM Management:"
    slog "  Status: ivm status $VM_NAME"
    slog "  Stop: ivm stop $VM_NAME"
    slog "  Start: ivm start $VM_NAME"
    slog "  Delete: ivm delete $VM_NAME"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
        --distro)
            DISTRO="$2"
            shift 2
            ;;
        --name)
            VM_NAME="$2"
            shift 2
            ;;
        --release)
            RELEASE="$2"
            shift 2
            ;;
        --username)
            USERNAME="$2"
            shift 2
            ;;
        --password)
            PASSWORD="$2"
            shift 2
            ;;
        --vcpus)
            VCPUS="$2"
            shift 2
            ;;
        --memory)
            MEMORY_MB="$2"
            shift 2
            ;;
        --disk-size)
            DISK_SIZE="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --bridge)
            BRIDGE_IF="$2"
            shift 2
            ;;
        --nix)
            INSTALL_NIX=true
            shift
            ;;
        --dotfiles)
            shift                 # Remove --dotfiles from arguments
            DOTFILES_OPTIONS="$*" # Capture all remaining arguments
            break                 # Exit the loop since --dotfiles must be last
            ;;
        --help | -h)
            usage
            exit 0
            ;;
        *)
            fail "Unknown option: $1"
            usage
            exit 1
            ;;
        esac
    done

    # Validate required arguments
    if [[ -z "${DISTRO:-}" ]]; then
        fail "Distribution is required. Use --distro option."
        usage
        exit 1
    fi
}

main() {
    slog "Starting Incus VM creation..."
    ivm_check_exists_prerequisites
    parse_args "$@"

    [[ -z "${SSH_KEY:-}" ]] && SSH_KEY=$(ssh_key_path)

    if [[ ! -f "$SSH_KEY" ]]; then
        fail "SSH public key not found at: $SSH_KEY"
        exit 1
    fi

    configure_distribution
    create_vm
    show_completion_info

    success "All done! Your $DISTRO VM '$VM_NAME' is ready to use."
    slog "Use username: $USERNAME and password: $PASSWORD to login."
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
