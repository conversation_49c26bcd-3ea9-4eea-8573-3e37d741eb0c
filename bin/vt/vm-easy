#!/usr/bin/env bash

set -euo pipefail

# shellcheck disable=SC1091
source "$(dirname "$0")/vm-utils"

unset USERNAME
unset PASSWORD

VCPUS="4"
MEMORY="8192"
DISK_SIZE="30G"
BRIDGE="default"
OS_VARIANT="linux"

cleanup_on_error() {
  warn "Cleaning up due to error..."
  sudo rm -f "$VM_DISK" 2>/dev/null || true
  rm -rf "$WORKDIR" 2>/dev/null || true
  stop_sudo_keepalive
}

trap cleanup_on_error ERR
trap 'cleanup_on_error; exit 1' INT TERM

configure_arch() {
  VM_NAME=${VM_NAME:-"arch-vt"}
  RELEASE=${RELEASE:="latest"}
  BASE_IMAGE_NAME="Arch-Linux-x86_64-cloudimg.qcow2"
  DOWNLOAD_URL="https://geo.mirror.pkgbuild.com/images/latest/${BASE_IMAGE_NAME}"
  USER_GROUPS=("wheel" "network" "storage")
  OS_VARIANT="archlinux"
}

configure_ubuntu() {
  VM_NAME=${VM_NAME:-"ubuntu-vt"}
  RELEASE=${RELEASE:="noble"}
  BASE_IMAGE_NAME="${RELEASE}-server-cloudimg-amd64.img"
  DOWNLOAD_URL="https://cloud-images.ubuntu.com/${RELEASE}/current/${BASE_IMAGE_NAME}"
  USER_GROUPS=("sudo" "adm" "sambashare")
  OS_VARIANT="ubuntu24.04"
}

configure_debian() {
  VM_NAME=${VM_NAME:="debian-vt"}
  RELEASE=${RELEASE:="bookworm"}
  BASE_IMAGE_NAME="debian-12-generic-amd64.qcow2"
  DOWNLOAD_URL="https://cloud.debian.org/images/cloud/bookworm/latest/${BASE_IMAGE_NAME}"
  USER_GROUPS=("sudo" "adm")
  OS_VARIANT="debian12"
}

configure_fedora() {
  VM_NAME=${VM_NAME:="fedora-vt"}
  RELEASE=${RELEASE:=42}
  BASE_IMAGE_NAME="Fedora-Cloud-Base-Generic-${RELEASE}-1.1.x86_64.qcow2"
  DOWNLOAD_URL="https://download.fedoraproject.org/pub/fedora/linux/releases/${RELEASE}/Cloud/x86_64/images/${BASE_IMAGE_NAME}"
  USER_GROUPS=("wheel" "network" "storage")
  OS_VARIANT="fedora41"
}

configure_distribution() {
  case "$DISTRO" in
  arch | "")
    configure_arch
    ;;
  ubuntu)
    configure_ubuntu
    ;;
  debian)
    configure_debian
    ;;
  fedora)
    configure_fedora
    ;;
  *)
    echo "Unknown distribution: $DISTRO"
    usage
    exit 1
    ;;
  esac

  USERNAME=${USERNAME:-"$DISTRO"}
  PASSWORD=${PASSWORD:=$USERNAME}
  PASSWORD_HASH=$(openssl passwd -6 "$PASSWORD")

  VTDIR="${VTDIR:-"$HOME/.virts"}"
  IMGDIR="${VTDIR}/images"
  WORKDIR="${VTDIR}/${VM_NAME}"

  CLOUD_INIT_DIR="${WORKDIR}/cloud-init"
  BASE_IMAGE="${IMGDIR}/${BASE_IMAGE_NAME}"

  USER_DATA="${CLOUD_INIT_DIR}/user-data"
  META_DATA="${CLOUD_INIT_DIR}/meta-data"
  VM_DISK="/var/lib/libvirt/images/${VM_NAME}.qcow2"

  SSH_KEY=$(ssh_key_path)
  mkdir -p "$CLOUD_INIT_DIR" "$IMGDIR" "$WORKDIR"
  sudo mkdir -p /var/lib/libvirt/images
}

download_base_image() {
  if [ -f "${BASE_IMAGE}" ]; then
    echo "Base image '${BASE_IMAGE}' already exists. Skipping download."
    return 0
  fi

  echo "Downloading base image from $DOWNLOAD_URL..."
  if ! wget -q --show-progress "$DOWNLOAD_URL" -O "${BASE_IMAGE}"; then
    echo "Error: Failed to download base image." >&2
  else
    echo "Base image downloaded successfully."
  fi
}

generate_cloud_init() {
  slog "Generating cloud-init configuration..."

  local pub_key
  pub_key=$(cat "$SSH_KEY")

  local -a packages=(
    "git"
    "micro"
    "tree"
    "curl"
    "wget"
    "unzip"
  )

  cat >"${CLOUD_INIT_DIR}/user-data" <<EOF
#cloud-config
hostname: $VM_NAME
manage_etc_hosts: true

users:
  - name: $USERNAME
    groups:
$(printf "      - %s\n" "${USER_GROUPS[@]}")
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: "$PASSWORD_HASH"
    ssh_authorized_keys:
      - "$pub_key"

package_update: true
packages:
$(printf "  - %s\n" "${packages[@]}")

ssh_pwauth: false
disable_root: false

runcmd:
  - systemctl enable --now sshd
EOF

  cat >"${CLOUD_INIT_DIR}/meta-data" <<EOF
instance-id: ${VM_NAME}-$(date +%s)
local-hostname: $VM_NAME
EOF

  if [[ ! -f "${CLOUD_INIT_DIR}/user-data" ||
    ! -f "${CLOUD_INIT_DIR}/meta-data" ]]; then
    fail "Failed to create one or more cloud-init configuration files"
    exit 1
  fi

  success "Cloud-init configuration generated"
}

check_prerequisites() {
  slog "Starting VM creation process for '${VM_NAME}'..."

  if ! groups "$USER" | grep -q '\blibvirt\b'; then
    fail "Error: User '$USER' is not in the 'libvirt' group." >&2
    fail "Please run: 'sudo usermod -aG libvirt \$USER' and then log out and back in." >&2
    exit 1
  fi

  if [ ! -f "$BASE_IMAGE" ]; then
    fail "Error: Base image '$BASE_IMAGE' not found." >&2
    fail "Please ensure it exists in the directory '$IMGDIR'." >&2
    exit 1
  fi

  if [ ! -f "$USER_DATA" ]; then
    fail "Error: User data file '$USER_DATA' not found." >&2
    fail "Please ensure it exists in the directory '$CLOUD_INIT_DIR'." >&2
    exit 1
  fi
  if [ ! -f "$META_DATA" ]; then
    fail "Error: Meta data file '$META_DATA' not found." >&2
    fail "Please ensure it exists in the directory '$CLOUD_INIT_DIR'." >&2
    exit 1
  fi

  if virsh dominfo "${VM_NAME}" &>/dev/null; then
    fail "Warning: A VM named '${VM_NAME}' already exists. Destroying and undefining it." >&2
    virsh destroy "${VM_NAME}" || true
    virsh undefine "${VM_NAME}" --remove-all-storage
  fi
  if [ -f "${VM_DISK}" ]; then
    echo "Warning: A disk file at '${VM_DISK}' already exists. Deleting it." >&2
    sudo rm -f "${VM_DISK}"
  fi
}

create_disk() {
  echo "Creating and resizing disk for ${VM_NAME} at ${VM_DISK}..."
  sudo qemu-img create -f qcow2 -b "${BASE_IMAGE}" -F qcow2 "${VM_DISK}" "${DISK_SIZE}"
  echo "Disk created successfully."
}

create_vm() {
  echo "Creating VM '${VM_NAME}'..."

  virt-install \
    --connect qemu:///system \
    --name "${VM_NAME}" \
    --memory "${MEMORY}" \
    --os-variant "${OS_VARIANT}" \
    --vcpus "${VCPUS}" \
    --import \
    --disk path="${VM_DISK}",device=disk,bus=virtio,format=qcow2 \
    --network network="${BRIDGE}",model=virtio \
    --cloud-init user-data="${USER_DATA}",meta-data="${META_DATA}" \
    --graphics none \
    --console pty,target_type=serial \
    --noautoconsole

  echo "✅ VM creation process started for '${VM_NAME}'."
  echo "Waiting for cloud-init to complete and the VM to get an IP address..."
  echo

  IP=""
  echo "Waiting for IP address..."
  while [ -z "$IP" ]; do
    IP=$(virsh domifaddr "${VM_NAME}" | awk '/ipv4/ {print $4}' | cut -d'/' -f1)
    sleep 2
  done

  echo "✅ VM is up! IP Address: ${IP}"
  echo
  echo "To connect via SSH, run:"
  echo "  ssh ${USERNAME}@${IP}"
  echo
  echo "The password is the one you set in the '${USER_DATA}' file."
  echo
  echo "To access the serial console, run:"
  echo "  virsh console ${VM_NAME}"
}

usage() {
  echo "Usage: $0 [options]"
  echo "Options:"
  echo "  --name USERNAME   Set the username for the VM (default: 'arch')"
  echo "  --password PASSWORD   Set the password for the VM (default: same as username)"
  echo "  --vcpus VCPUS         Set the number of vCPUs (default: 4)"
  echo "  --memory MEMORY       Set the amount of memory in MB (default: 8192)"
  echo "  --distro DISTRO       Set the distribution (arch or ubuntu, default: arch)"
  echo "  --name VM_NAME        Set the name of the VM (default: 'arch')"
  echo "  --release RELEASE     Set the release version (default: 'latest')"
  echo "  --bridge BRIDGE       Set the network bridge (default: 'default')"
  echo "  --disk-size SIZE      Set the disk size (default: '30G')"
  echo "  --help                Show this help message"
}

parse_args() {
  while [[ $# -gt 0 ]]; do
    case "$1" in
    --distro)
      DISTRO="$2"
      shift 2
      ;;
    --name)
      VM_NAME="$2"
      shift 2
      ;;
    --release)
      RELEASE="$2"
      shift 2
      ;;
    --username)
      USERNAME="$2"
      shift 2
      ;;
    --password)
      PASSWORD="$2"
      shift 2
      ;;
    --vcpus)
      VCPUS="$2"
      shift 2
      ;;
    --memory)
      MEMORY="$2"
      shift 2
      ;;
    --disk-size)
      DISK_SIZE="$2"
      shift 2
      ;;
    --bridge)
      BRIDGE="$2"
      shift 2
      ;;
    --help)
      usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      usage
      exit 1
      ;;
    esac
  done
}

main() {
  virt_check_prerequisites

  parse_args "$@"
  configure_distribution

  slog "Starting VM creation process for '${VM_NAME}'..."
  echo

  echo "Using vCPUs: ${VCPUS}"
  echo "Using memory: ${MEMORY} MB"
  echo "Using disk size: ${DISK_SIZE}"
  echo

  slog "Using username: ${USERNAME}"
  slog "Using password: ${PASSWORD}"
  echo "Press Enter to continue or Ctrl+C to cancel..."
  read -r

  download_base_image
  generate_cloud_init
  check_prerequisites

  create_disk
  create_vm
}

main "$@"
